# Deployment Guide - Rescue Mission Path Finder

This guide covers both local development and cloud deployment options for the Rescue Mission Path Finder game.

## Project Structure

```
rescue-mission-pathfinder/
├── rescue_mission_pathfinder.py  # Original Pygame version (local only)
├── app.py                        # Flask web application
├── templates/
│   └── index.html               # Web UI template
├── static/
│   └── game.js                  # Frontend JavaScript
├── requirements.txt             # Python dependencies
├── Procfile                     # Render deployment config
├── render.yaml                  # Render service configuration
├── runtime.txt                  # Python version specification
├── demo_pathfinding.py          # Algorithm demonstration
├── test_game.py                 # Test suite
└── README.md                    # Documentation
```

## Local Development

### Option 1: Original Pygame Version (Desktop)

1. **Install Dependencies**:
   ```bash
   pip install pygame
   ```

2. **Run the Game**:
   ```bash
   python rescue_mission_pathfinder.py
   ```

3. **Features**:
   - Full desktop GUI with Pygame
   - Mouse interaction
   - Visual pathfinding
   - Random events

### Option 2: Web Version (Local Server)

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Web Server**:
   ```bash
   python app.py
   ```

3. **Access the Game**:
   - Open browser to `http://localhost:5000`
   - Modern web interface
   - Responsive design
   - Same algorithm functionality

## Cloud Deployment on Render

### Prerequisites

1. **GitHub Repository**: Push your code to GitHub
2. **Render Account**: Sign up at [render.com](https://render.com)

### Deployment Steps

1. **Connect Repository**:
   - Go to Render Dashboard
   - Click "New +" → "Web Service"
   - Connect your GitHub repository

2. **Configure Service**:
   - **Name**: `rescue-mission-pathfinder`
   - **Environment**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn app:app`

3. **Environment Variables** (Optional):
   - `PYTHON_VERSION`: `3.9.16`

4. **Deploy**:
   - Click "Create Web Service"
   - Render will automatically deploy your app
   - You'll get a URL like `https://rescue-mission-pathfinder.onrender.com`

### Alternative: Using render.yaml

1. **Place render.yaml in root directory** (already included)
2. **Connect repository to Render**
3. **Render will auto-detect the configuration**

## GitHub Repository Updates

### Initial Setup

```bash
# Initialize git repository (if not already done)
git init

# Add all files
git add .

# Commit changes
git commit -m "Add web version and deployment configuration"

# Add remote repository
git remote add origin https://github.com/yourusername/rescue-mission-pathfinder.git

# Push to GitHub
git push -u origin main
```

### Updating Existing Repository

```bash
# Add new files
git add .

# Commit changes
git commit -m "Update with improved web UI and deployment config"

# Push changes
git push origin main
```

## File Descriptions for Deployment

### Core Application Files

- **`app.py`**: Flask web application with REST API endpoints
- **`templates/index.html`**: Modern responsive web interface
- **`static/game.js`**: Frontend JavaScript for game logic

### Configuration Files

- **`requirements.txt`**: Python package dependencies
- **`Procfile`**: Tells Render how to run the application
- **`render.yaml`**: Render service configuration
- **`runtime.txt`**: Specifies Python version

### Legacy Files (Local Only)

- **`rescue_mission_pathfinder.py`**: Original Pygame version
- **`demo_pathfinding.py`**: Command-line demonstration
- **`test_game.py`**: Test suite

## Environment Differences

| Feature | Pygame Version | Web Version |
|---------|---------------|-------------|
| Platform | Desktop only | Any browser |
| UI | Native GUI | HTML5/CSS/JS |
| Deployment | Local only | Cloud-ready |
| Interaction | Mouse/Keyboard | Mouse/Touch |
| Performance | High | Good |
| Accessibility | Limited | High |

## Troubleshooting

### Local Development Issues

1. **Pygame not found**:
   ```bash
   pip install pygame
   ```

2. **Flask not found**:
   ```bash
   pip install flask
   ```

3. **Port already in use**:
   - Change port in `app.py`: `app.run(port=5001)`

### Deployment Issues

1. **Build fails on Render**:
   - Check `requirements.txt` format
   - Ensure Python version compatibility

2. **App doesn't start**:
   - Verify `Procfile` syntax
   - Check logs in Render dashboard

3. **Static files not loading**:
   - Ensure `static/` and `templates/` directories exist
   - Check file paths in HTML

## Performance Optimization

### For Production Deployment

1. **Use Gunicorn** (already configured):
   ```bash
   gunicorn app:app --workers 4
   ```

2. **Enable Gzip Compression**:
   ```python
   from flask_compress import Compress
   Compress(app)
   ```

3. **Add Caching Headers**:
   ```python
   @app.after_request
   def after_request(response):
       response.headers["Cache-Control"] = "public, max-age=300"
       return response
   ```

## Security Considerations

1. **Environment Variables**: Store sensitive data in environment variables
2. **CORS**: Configure CORS for production if needed
3. **Rate Limiting**: Add rate limiting for API endpoints
4. **Input Validation**: Validate all user inputs

## Monitoring and Maintenance

1. **Logs**: Monitor application logs in Render dashboard
2. **Health Checks**: Render automatically monitors app health
3. **Updates**: Push to GitHub to trigger automatic redeployment
4. **Scaling**: Upgrade Render plan for higher traffic

## Cost Considerations

- **Render Free Tier**: Suitable for educational projects
- **Paid Plans**: Required for production use
- **Alternative Platforms**: Heroku, Vercel, Railway

## Next Steps

1. Deploy to Render using this guide
2. Test all functionality in production
3. Share the live URL for demonstration
4. Consider adding advanced features like user accounts
5. Monitor usage and performance metrics
