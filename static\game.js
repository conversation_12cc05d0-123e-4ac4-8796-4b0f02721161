// Rescue Mission - Path Finder Game JavaScript

class RescueMissionGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nodes = [];
        this.edges = [];
        this.gameState = 'menu';
        this.startNode = null;
        this.endNode = null;
        this.playerPath = [];
        this.optimalPath = [];
        this.optimalDistance = 0;
        this.currentScore = 0;
        this.round = 1;
        this.maxRounds = 3;
        this.totalScore = 0;
        
        this.setupEventListeners();
        this.loadGameData();
    }
    
    setupEventListeners() {
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseleave', () => this.hideNodeInfo());
    }
    
    async loadGameData() {
        try {
            const response = await fetch('/api/game-data');
            const data = await response.json();
            this.nodes = data.nodes;
            this.edges = data.edges;
            this.draw();
        } catch (error) {
            console.error('Error loading game data:', error);
        }
    }
    
    getNodeAtPosition(x, y) {
        for (let node of this.nodes) {
            const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
            if (distance <= 25) {
                return node;
            }
        }
        return null;
    }
    
    handleCanvasClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const clickedNode = this.getNodeAtPosition(x, y);
        if (!clickedNode) return;
        
        if (this.gameState === 'selecting_start') {
            if (clickedNode.node_type === 'Hospital') {
                this.startNode = clickedNode;
                this.playerPath = [clickedNode.id];
                this.gameState = 'selecting_end';
                this.updateGameState('Select an <strong>Accident Site</strong> (red) as your destination');
                this.draw();
            }
        } else if (this.gameState === 'selecting_end') {
            if (clickedNode.node_type === 'Accident Site') {
                this.endNode = clickedNode;
                this.gameState = 'playing';
                this.updateGameState('Build your path by clicking connected nodes. Click "Show Optimal Path" when ready!');
                document.getElementById('findPathBtn').disabled = false;
                document.getElementById('resetBtn').disabled = false;
                this.draw();
            }
        } else if (this.gameState === 'playing') {
            this.addNodeToPath(clickedNode);
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const hoveredNode = this.getNodeAtPosition(x, y);
        if (hoveredNode) {
            this.showNodeInfo(hoveredNode, e.clientX, e.clientY);
        } else {
            this.hideNodeInfo();
        }
    }
    
    showNodeInfo(node, x, y) {
        const nodeInfo = document.getElementById('nodeInfo');
        nodeInfo.innerHTML = `<strong>${node.name}</strong><br>Type: ${node.node_type}<br>ID: ${node.id}`;
        nodeInfo.style.left = (x + 10) + 'px';
        nodeInfo.style.top = (y - 50) + 'px';
        nodeInfo.style.display = 'block';
    }
    
    hideNodeInfo() {
        document.getElementById('nodeInfo').style.display = 'none';
    }
    
    addNodeToPath(clickedNode) {
        if (this.playerPath.length === 0) {
            this.playerPath = [clickedNode.id];
        } else {
            const lastNode = this.playerPath[this.playerPath.length - 1];
            
            // Check if nodes are connected
            const isConnected = this.edges.some(edge => 
                (edge.from_node === lastNode && edge.to_node === clickedNode.id && !edge.is_blocked) ||
                (edge.from_node === clickedNode.id && edge.to_node === lastNode && !edge.is_blocked)
            );
            
            if (isConnected && !this.playerPath.includes(clickedNode.id)) {
                this.playerPath.push(clickedNode.id);
            } else if (clickedNode.id === lastNode) {
                // Allow clicking same node to remove it
                this.playerPath.pop();
            } else if (this.playerPath.includes(clickedNode.id)) {
                // Truncate path to clicked node
                const index = this.playerPath.indexOf(clickedNode.id);
                this.playerPath = this.playerPath.slice(0, index + 1);
            }
        }
        
        this.updatePathDisplay();
        this.draw();
    }
    
    updatePathDisplay() {
        const pathDisplay = document.getElementById('pathDisplay');
        const pathInfo = document.getElementById('pathInfo');
        
        if (this.playerPath.length > 0) {
            const pathNames = this.playerPath.map(nodeId => 
                this.nodes.find(n => n.id === nodeId).name
            );
            pathInfo.innerHTML = pathNames.join(' → ');
            pathDisplay.style.display = 'block';
        } else {
            pathDisplay.style.display = 'none';
        }
    }
    
    updateGameState(message) {
        const gameState = document.getElementById('gameState');
        gameState.innerHTML = `<div class="alert alert-info alert-custom">${message}</div>`;
    }
    
    draw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw edges first
        this.drawEdges();
        
        // Draw paths
        if (this.optimalPath.length > 1) {
            this.drawPath(this.optimalPath, '#28a745', 8, 'Optimal');
        }
        
        if (this.playerPath.length > 1) {
            this.drawPath(this.playerPath, '#007bff', 6, 'Player');
        }
        
        // Draw nodes
        this.drawNodes();
    }
    
    drawEdges() {
        this.ctx.strokeStyle = '#dee2e6';
        this.ctx.lineWidth = 2;
        
        const drawnEdges = new Set();
        
        for (let edge of this.edges) {
            const edgeKey = `${Math.min(edge.from_node, edge.to_node)}-${Math.max(edge.from_node, edge.to_node)}`;
            if (drawnEdges.has(edgeKey)) continue;
            drawnEdges.add(edgeKey);
            
            const fromNode = this.nodes.find(n => n.id === edge.from_node);
            const toNode = this.nodes.find(n => n.id === edge.to_node);
            
            if (fromNode && toNode) {
                this.ctx.strokeStyle = edge.is_blocked ? '#dc3545' : 
                                     edge.weight > edge.original_weight ? '#fd7e14' : '#dee2e6';
                
                this.ctx.beginPath();
                this.ctx.moveTo(fromNode.x, fromNode.y);
                this.ctx.lineTo(toNode.x, toNode.y);
                this.ctx.stroke();
                
                // Draw weight label
                const midX = (fromNode.x + toNode.x) / 2;
                const midY = (fromNode.y + toNode.y) / 2;
                
                this.ctx.fillStyle = '#495057';
                this.ctx.font = '12px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(Math.round(edge.weight), midX, midY - 5);
            }
        }
    }
    
    drawPath(path, color, width, label) {
        if (path.length < 2) return;
        
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = width;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
        
        this.ctx.beginPath();
        for (let i = 0; i < path.length - 1; i++) {
            const fromNode = this.nodes.find(n => n.id === path[i]);
            const toNode = this.nodes.find(n => n.id === path[i + 1]);
            
            if (fromNode && toNode) {
                if (i === 0) {
                    this.ctx.moveTo(fromNode.x, fromNode.y);
                }
                this.ctx.lineTo(toNode.x, toNode.y);
            }
        }
        this.ctx.stroke();
    }
    
    drawNodes() {
        for (let node of this.nodes) {
            // Node circle
            this.ctx.fillStyle = node.color;
            this.ctx.beginPath();
            this.ctx.arc(node.x, node.y, 25, 0, 2 * Math.PI);
            this.ctx.fill();
            
            // Node border
            this.ctx.strokeStyle = '#333';
            this.ctx.lineWidth = 2;
            
            // Highlight selected nodes
            if (this.startNode && node.id === this.startNode.id) {
                this.ctx.strokeStyle = '#ffc107';
                this.ctx.lineWidth = 4;
            } else if (this.endNode && node.id === this.endNode.id) {
                this.ctx.strokeStyle = '#6f42c1';
                this.ctx.lineWidth = 4;
            } else if (this.playerPath.includes(node.id)) {
                this.ctx.strokeStyle = '#007bff';
                this.ctx.lineWidth = 3;
            }
            
            this.ctx.stroke();
            
            // Node ID
            this.ctx.fillStyle = 'white';
            this.ctx.font = 'bold 14px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(node.id, node.x, node.y + 5);
            
            // Node name
            this.ctx.fillStyle = '#333';
            this.ctx.font = '12px Arial';
            this.ctx.fillText(node.name, node.x, node.y + 45);
        }
    }
}

// Game instance
let game;

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', function() {
    game = new RescueMissionGame();
});

// Game control functions
function startNewGame() {
    game.gameState = 'selecting_start';
    game.startNode = null;
    game.endNode = null;
    game.playerPath = [];
    game.optimalPath = [];
    game.currentScore = 0;
    
    document.getElementById('findPathBtn').disabled = true;
    document.getElementById('resetBtn').disabled = true;
    document.getElementById('scoreDisplay').style.display = 'none';
    document.getElementById('pathDisplay').style.display = 'none';
    document.getElementById('optimalDisplay').style.display = 'none';
    
    game.updateGameState('Select a <strong>Hospital</strong> (green) as your starting point');
    game.draw();
}

async function findOptimalPath() {
    if (!game.startNode || !game.endNode) return;
    
    document.getElementById('loading').style.display = 'block';
    
    try {
        const response = await fetch('/api/find-path', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start: game.startNode.id,
                end: game.endNode.id
            })
        });
        
        const data = await response.json();
        game.optimalPath = data.path;
        game.optimalDistance = data.distance;
        
        // Calculate score
        const scoreResponse = await fetch('/api/calculate-score', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                player_path: game.playerPath,
                optimal_distance: game.optimalDistance
            })
        });
        
        const scoreData = await scoreResponse.json();
        game.currentScore = scoreData.score;
        
        // Update displays
        updateScoreDisplay(game.currentScore);
        updateOptimalDisplay(data.path_names, data.distance);
        
        game.draw();
        
    } catch (error) {
        console.error('Error finding optimal path:', error);
    } finally {
        document.getElementById('loading').style.display = 'none';
    }
}

function updateScoreDisplay(score) {
    const scoreDisplay = document.getElementById('scoreDisplay');
    const currentScore = document.getElementById('currentScore');
    
    currentScore.textContent = score;
    scoreDisplay.style.display = 'block';
    
    // Update score styling based on performance
    scoreDisplay.className = 'score-display ';
    if (score >= 80) {
        scoreDisplay.className += 'score-excellent';
    } else if (score >= 50) {
        scoreDisplay.className += 'score-good';
    } else {
        scoreDisplay.className += 'score-poor';
    }
}

function updateOptimalDisplay(pathNames, distance) {
    const optimalDisplay = document.getElementById('optimalDisplay');
    const optimalInfo = document.getElementById('optimalInfo');
    
    optimalInfo.innerHTML = `${pathNames.join(' → ')}<br><small>Distance: ${distance.toFixed(1)} minutes</small>`;
    optimalDisplay.style.display = 'block';
}

function resetPath() {
    if (game.startNode) {
        game.playerPath = [game.startNode.id];
        game.updatePathDisplay();
        game.draw();
    }
}

async function applyRandomEvent() {
    try {
        const response = await fetch('/api/random-event', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.event_message) {
            const gameState = document.getElementById('gameState');
            gameState.innerHTML = `<div class="alert alert-warning alert-custom"><strong>Random Event!</strong> ${data.event_message}</div>`;
        }
        
        // Update game data
        game.nodes = data.game_data.nodes;
        game.edges = data.game_data.edges;
        game.draw();
        
    } catch (error) {
        console.error('Error applying random event:', error);
    }
}
