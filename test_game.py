"""
Test script for Rescue Mission - Path Finder Game
Tests core functionality without GUI
"""

import sys
import os

# Add current directory to path to import the game module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rescue_mission_pathfinder import RescueMissionGame, NodeType

def test_dijkstra_algorithm():
    """Test the Dijkstra's algorithm implementation"""
    print("Testing Dijkstra's Algorithm...")
    
    # Create game instance
    game = RescueMissionGame()
    
    # Test pathfinding from hospital (0) to accident site (3)
    start_node = 0  # Central Hospital
    end_node = 3    # Highway Crash
    
    path, distance = game.dijkstra(start_node, end_node)
    
    print(f"Path from {game.nodes[start_node].name} to {game.nodes[end_node].name}:")
    print(f"Nodes: {path}")
    print(f"Distance: {distance}")
    
    # Print path with node names
    if path:
        path_names = [game.nodes[node_id].name for node_id in path]
        print(f"Path: {' -> '.join(path_names)}")
    
    return len(path) > 0 and distance < float('infinity')

def test_graph_structure():
    """Test the graph structure and connectivity"""
    print("\nTesting Graph Structure...")
    
    game = RescueMissionGame()
    
    # Count nodes by type
    hospitals = sum(1 for node in game.nodes.values() if node.node_type == NodeType.HOSPITAL)
    accident_sites = sum(1 for node in game.nodes.values() if node.node_type == NodeType.ACCIDENT_SITE)
    checkpoints = sum(1 for node in game.nodes.values() if node.node_type == NodeType.CHECKPOINT)
    
    print(f"Hospitals: {hospitals}")
    print(f"Accident Sites: {accident_sites}")
    print(f"Checkpoints: {checkpoints}")
    print(f"Total Nodes: {len(game.nodes)}")
    print(f"Total Edges: {len(game.edges)}")
    
    # Test adjacency list
    total_connections = sum(len(neighbors) for neighbors in game.adjacency_list.values())
    print(f"Total Connections: {total_connections}")
    
    return hospitals > 0 and accident_sites > 0 and checkpoints > 0

def test_path_calculation():
    """Test path distance calculation"""
    print("\nTesting Path Distance Calculation...")
    
    game = RescueMissionGame()
    
    # Test a simple path
    test_path = [0, 7, 10, 3]  # Central Hospital -> Police Station -> Emergency Dispatch -> Highway Crash
    distance = game.calculate_path_distance(test_path)
    
    print(f"Test path: {test_path}")
    print(f"Distance: {distance}")
    
    return distance > 0 and distance < float('infinity')

def test_scoring_system():
    """Test the scoring system"""
    print("\nTesting Scoring System...")
    
    game = RescueMissionGame()
    
    # Set up a test scenario
    game.start_node = 0
    game.end_node = 3
    game.optimal_path, game.optimal_distance = game.dijkstra(0, 3)
    
    # Test perfect score (same path as optimal)
    game.player_path = game.optimal_path.copy()
    game.player_distance = game.optimal_distance
    perfect_score = game.calculate_score()
    
    # Test suboptimal path
    game.player_path = [0, 7, 8, 10, 3]  # Longer path
    game.player_distance = game.calculate_path_distance(game.player_path)
    suboptimal_score = game.calculate_score()
    
    print(f"Optimal path: {game.optimal_path}")
    print(f"Optimal distance: {game.optimal_distance}")
    print(f"Perfect score: {perfect_score}")
    print(f"Suboptimal score: {suboptimal_score}")
    
    return perfect_score == 100 and suboptimal_score < perfect_score

def test_random_events():
    """Test random events system"""
    print("\nTesting Random Events...")
    
    game = RescueMissionGame()
    
    # Store original state
    original_edges = [(e.from_node, e.to_node, e.weight, e.is_blocked) for e in game.edges]
    
    # Apply random event
    game.apply_random_event()
    
    # Check if anything changed
    changed = False
    for i, edge in enumerate(game.edges):
        orig = original_edges[i]
        if (edge.weight != orig[2] or edge.is_blocked != orig[3]):
            changed = True
            break
    
    print(f"Random event applied: {changed}")
    
    # Reset events
    game.reset_events()
    
    # Check if reset worked
    reset_worked = True
    for i, edge in enumerate(game.edges):
        orig = original_edges[i]
        if (edge.weight != orig[2] or edge.is_blocked != False):
            reset_worked = False
            break
    
    print(f"Reset worked: {reset_worked}")
    
    return reset_worked

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("RESCUE MISSION - PATH FINDER GAME TESTS")
    print("=" * 50)
    
    tests = [
        ("Graph Structure", test_graph_structure),
        ("Dijkstra Algorithm", test_dijkstra_algorithm),
        ("Path Calculation", test_path_calculation),
        ("Scoring System", test_scoring_system),
        ("Random Events", test_random_events),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "PASS" if result else "FAIL"
            print(f"\n{test_name}: {status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("All tests passed! The game is ready to play.")
    else:
        print("Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    run_all_tests()
