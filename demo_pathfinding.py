"""
Demo script showing <PERSON><PERSON><PERSON>'s algorithm in action
This script demonstrates the pathfinding without the GUI
"""

from rescue_mission_pathfinder import Rescue<PERSON>issionG<PERSON>, NodeType

def demonstrate_pathfinding():
    """Demonstrate pathfinding between different locations"""
    print("🚑 RESCUE MISSION - PATHFINDING DEMONSTRATION")
    print("=" * 60)
    
    # Create game instance
    game = RescueMissionGame()
    
    # Show all locations
    print("\n📍 CITY MAP LOCATIONS:")
    print("-" * 40)
    
    hospitals = []
    accident_sites = []
    checkpoints = []
    
    for node_id, node in game.nodes.items():
        if node.node_type == NodeType.HOSPITAL:
            hospitals.append((node_id, node.name))
        elif node.node_type == NodeType.ACCIDENT_SITE:
            accident_sites.append((node_id, node.name))
        else:
            checkpoints.append((node_id, node.name))
    
    print("🏥 HOSPITALS:")
    for node_id, name in hospitals:
        print(f"   {node_id}: {name}")
    
    print("\n🚨 ACCIDENT SITES:")
    for node_id, name in accident_sites:
        print(f"   {node_id}: {name}")
    
    print("\n🔵 CHECKPOINTS:")
    for node_id, name in checkpoints:
        print(f"   {node_id}: {name}")
    
    # Demonstrate pathfinding examples
    print("\n🛣️  PATHFINDING EXAMPLES:")
    print("=" * 60)
    
    examples = [
        (0, 3, "Central Hospital", "Highway Crash"),
        (1, 4, "City Hospital", "Downtown Accident"),
        (2, 5, "Emergency Center", "Bridge Incident"),
        (0, 6, "Central Hospital", "Tunnel Crash"),
    ]
    
    for i, (start, end, start_name, end_name) in enumerate(examples, 1):
        print(f"\n📋 EXAMPLE {i}: {start_name} → {end_name}")
        print("-" * 50)
        
        # Find optimal path
        path, distance = game.dijkstra(start, end)
        
        if path:
            # Show path with names
            path_names = [game.nodes[node_id].name for node_id in path]
            print(f"🎯 Optimal Path: {' → '.join(path_names)}")
            print(f"📏 Total Distance: {distance:.1f} minutes")
            print(f"🔢 Node Sequence: {path}")
            
            # Show step-by-step breakdown
            print("📝 Step-by-step:")
            for j in range(len(path) - 1):
                from_node = path[j]
                to_node = path[j + 1]
                
                # Find edge weight
                step_distance = 0
                for neighbor, weight in game.adjacency_list.get(from_node, []):
                    if neighbor == to_node:
                        step_distance = weight
                        break
                
                print(f"   Step {j+1}: {game.nodes[from_node].name} → {game.nodes[to_node].name} ({step_distance} min)")
        else:
            print("❌ No path found!")
    
    # Demonstrate random events
    print("\n🎲 RANDOM EVENTS DEMONSTRATION:")
    print("=" * 60)
    
    print("\n📊 Original state:")
    original_path, original_distance = game.dijkstra(0, 3)
    print(f"Path from Central Hospital to Highway Crash: {original_distance:.1f} minutes")
    
    # Apply random event
    print("\n⚡ Applying random event...")
    game.apply_random_event()
    
    # Check new path
    new_path, new_distance = game.dijkstra(0, 3)
    print(f"New path after event: {new_distance:.1f} minutes")
    
    if new_distance != original_distance:
        print("🚧 Route conditions changed due to random event!")
        if new_distance > original_distance:
            print("   → Travel time increased (traffic or detour)")
        else:
            print("   → Travel time decreased (road cleared)")
    else:
        print("   → No significant change in optimal route")
    
    # Reset and show restoration
    print("\n🔄 Resetting events...")
    game.reset_events()
    reset_path, reset_distance = game.dijkstra(0, 3)
    print(f"Path after reset: {reset_distance:.1f} minutes")
    
    if reset_distance == original_distance:
        print("✅ Successfully restored to original conditions")
    
    print("\n🎮 GAME MECHANICS:")
    print("=" * 60)
    print("1. Player selects start (hospital) and end (accident site)")
    print("2. Player manually builds path by clicking connected nodes")
    print("3. Game calculates optimal path using Dijkstra's algorithm")
    print("4. Player's path is compared with optimal path for scoring")
    print("5. Random events add dynamic challenges")
    print("6. Score based on path efficiency (100 = perfect, 20+ = valid path)")
    
    print("\n🏆 EDUCATIONAL VALUE:")
    print("=" * 60)
    print("• Visualizes graph theory concepts")
    print("• Demonstrates Dijkstra's algorithm in action")
    print("• Shows real-world applications (GPS, emergency routing)")
    print("• Teaches algorithm efficiency and optimization")
    print("• Provides hands-on experience with pathfinding")

if __name__ == "__main__":
    demonstrate_pathfinding()
