<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rescue Mission - Path Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .game-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin: 20px;
            padding: 30px;
        }
        
        .game-header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .game-header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .game-canvas {
            border: 3px solid #e9ecef;
            border-radius: 15px;
            background: #ffffff;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            cursor: crosshair;
            transition: all 0.3s ease;
        }
        
        .game-canvas:hover {
            border-color: #667eea;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1), 0 0 20px rgba(102, 126, 234, 0.3);
        }
        
        .control-panel {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            margin: 5px;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success-custom {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-danger-custom {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .btn-warning-custom {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .game-info {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .score-display {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .score-excellent {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .score-good {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .score-poor {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .legend {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px;
            padding: 10px 15px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid #333;
        }
        
        .path-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }
        
        .alert-custom {
            border-radius: 15px;
            border: none;
            padding: 15px 20px;
            margin: 10px 0;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .node-info {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }
        
        @media (max-width: 768px) {
            .game-container {
                margin: 10px;
                padding: 15px;
            }
            
            .game-header h1 {
                font-size: 2rem;
            }
            
            .legend {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="game-container">
            <div class="game-header">
                <h1><i class="fas fa-ambulance"></i> Rescue Mission - Path Finder</h1>
                <p class="lead">Find the shortest path from hospitals to accident sites using Dijkstra's algorithm</p>
            </div>
            
            <div class="row">
                <div class="col-lg-8">
                    <div class="game-info">
                        <div class="legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #28a745;"></div>
                                <span><i class="fas fa-hospital"></i> Hospitals</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #dc3545;"></div>
                                <span><i class="fas fa-car-crash"></i> Accident Sites</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #007bff;"></div>
                                <span><i class="fas fa-checkpoint"></i> Checkpoints</span>
                            </div>
                        </div>
                    </div>
                    
                    <canvas id="gameCanvas" class="game-canvas" width="1200" height="800"></canvas>
                    <div class="node-info" id="nodeInfo"></div>
                </div>
                
                <div class="col-lg-4">
                    <div class="control-panel">
                        <h4 class="text-center mb-4"><i class="fas fa-gamepad"></i> Game Controls</h4>
                        
                        <div class="game-state" id="gameState">
                            <div class="alert alert-info alert-custom">
                                <strong>Welcome!</strong> Click "Start New Game" to begin your rescue mission.
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary-custom btn-custom" onclick="startNewGame()">
                                <i class="fas fa-play"></i> Start New Game
                            </button>
                            <button class="btn btn-success-custom btn-custom" onclick="findOptimalPath()" disabled id="findPathBtn">
                                <i class="fas fa-route"></i> Show Optimal Path
                            </button>
                            <button class="btn btn-warning-custom btn-custom" onclick="resetPath()" disabled id="resetBtn">
                                <i class="fas fa-undo"></i> Reset Path
                            </button>
                            <button class="btn btn-danger-custom btn-custom" onclick="applyRandomEvent()">
                                <i class="fas fa-exclamation-triangle"></i> Random Event
                            </button>
                        </div>
                        
                        <div class="score-display" id="scoreDisplay" style="display: none;">
                            Score: <span id="currentScore">0</span>/100
                        </div>
                        
                        <div class="path-display" id="pathDisplay" style="display: none;">
                            <h6><i class="fas fa-map-marked-alt"></i> Current Path:</h6>
                            <div id="pathInfo">No path selected</div>
                        </div>
                        
                        <div class="path-display" id="optimalDisplay" style="display: none;">
                            <h6><i class="fas fa-trophy"></i> Optimal Path:</h6>
                            <div id="optimalInfo">Not calculated</div>
                        </div>
                        
                        <div class="loading" id="loading">
                            <div class="spinner"></div>
                            <p>Calculating optimal path...</p>
                        </div>
                    </div>
                    
                    <div class="game-info mt-3">
                        <h5><i class="fas fa-info-circle"></i> How to Play</h5>
                        <ol>
                            <li>Click a <strong>Hospital</strong> (green) to start</li>
                            <li>Click an <strong>Accident Site</strong> (red) as destination</li>
                            <li>Build your path by clicking connected nodes</li>
                            <li>Compare with the optimal path</li>
                            <li>Score based on efficiency!</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='game.js') }}"></script>
</body>
</html>
