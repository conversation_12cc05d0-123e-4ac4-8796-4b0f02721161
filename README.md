# Rescue Mission - Path Finder Game

A Pygame-based educational game that teaches <PERSON><PERSON><PERSON>'s algorithm through an interactive pathfinding experience. Perfect for BTech 5th semester AIML students to understand graph algorithms in a practical, engaging way.

##  Game Overview

In "Rescue Mission - Path Finder", you play as an emergency coordinator who must find the fastest route from hospitals to accident sites. The game challenges you to manually select paths and compares your choices with the optimal path calculated using <PERSON><PERSON><PERSON>'s algorithm.

##  Features

- **Interactive Graph Visualization**: City map with hospitals (green), accident sites (red), and checkpoints (blue)
- **Dijkstra's Algorithm Implementation**: Real-time shortest path calculation
- **Educational Scoring System**: Compare your path efficiency with the optimal solution
- **Random Events**: Dynamic roadblocks and traffic that change during gameplay
- **Multiple Rounds**: 3 rounds with increasing difficulty
- **Real-time Feedback**: Visual path comparison and scoring

## Installation

1. **Install Python** (3.7 or higher)
2. **Install Pygame**:
   ```bash
   pip install pygame
   ```
   Or using the requirements file:
   ```bash
   pip install -r requirements.txt
   ```

## How to Play

1. **Run the game**:
   ```bash
   python rescue_mission_pathfinder.py
   ```

2. **Game Flow**:
   - Click "Start Game" from the main menu
   - **Round 1-3**: Each round consists of:
     - Select a **Hospital** (green node) as your starting point
     - Select an **Accident Site** (red node) as your destination
     - Click connected nodes to build your rescue path
     - Press **SPACE** when you reach the destination
     - View results comparing your path vs optimal path

3. **Controls**:
   - **Mouse Click**: Select nodes and build path
   - **SPACE**: Finish current round (when at destination)
   - **R**: Reset your current path
   - **ENTER**: Start game / Next round
   - **ESC**: Quit game

## Educational Value

### Dijkstra's Algorithm Concepts Demonstrated:
- **Graph Representation**: Nodes and weighted edges
- **Shortest Path Problem**: Finding minimum cost routes
- **Greedy Algorithm**: How Dijkstra makes optimal local choices
- **Priority Queue**: Efficient node selection
- **Dynamic Programming**: Building optimal solutions step by step

### Learning Outcomes:
- Understand graph theory fundamentals
- Visualize how Dijkstra's algorithm works
- Compare manual pathfinding with algorithmic solutions
- Learn about real-world applications (GPS, network routing, etc.)

## Scoring System

- **Perfect Score (100 points)**: Your path matches the optimal path exactly
- **Efficiency Score**: Based on your path length vs optimal path length
- **Minimum Score (20 points)**: For finding any valid path to the destination
- **Total Score**: Sum of all rounds (max 300 points)

## Random Events

- **Roadblocks**: Randomly block certain paths during gameplay
- **Traffic Jams**: Increase travel time on some roads
- **Dynamic Changes**: Events occur between rounds to add challenge

## Code Structure

```
rescue_mission_pathfinder.py
├── Node Class: Represents locations (hospitals, accident sites, checkpoints)
├── Edge Class: Represents roads with weights and dynamic properties
├── GameState Enum: Manages different game phases
├── RescueMissionGame Class:
│   ├── Graph Management: Node/edge creation and adjacency lists
│   ├── Dijkstra Implementation: Shortest path algorithm
│   ├── Game Logic: State management and scoring
│   ├── Pygame Interface: Drawing and user interaction
│   └── Event System: Random roadblocks and traffic
```

## Technical Implementation

### Graph Representation:
- **Adjacency List**: Efficient storage for sparse graphs
- **Bidirectional Edges**: Roads work in both directions
- **Dynamic Weights**: Support for traffic and roadblock events

### Dijkstra's Algorithm:
- **Priority Queue**: Using Python's heapq for efficient min-heap
- **Distance Tracking**: Maintains shortest distances to all nodes
- **Path Reconstruction**: Builds actual path from start to destination

### Pygame Features:
- **Real-time Rendering**: Smooth 60 FPS gameplay
- **Interactive UI**: Mouse-based node selection
- **Visual Feedback**: Color-coded paths and node states

## For Students

This project demonstrates:
- **Object-Oriented Programming**: Classes, inheritance, and encapsulation
- **Algorithm Implementation**: Dijkstra's algorithm from scratch
- **Data Structures**: Graphs, priority queues, and adjacency lists
- **Game Development**: Event handling, graphics, and user interaction
- **Software Engineering**: Code organization and documentation

## Possible Extensions

- Add more complex city layouts
- Implement A* algorithm for comparison
- Add multiplayer competitive mode
- Include different vehicle types with varying speeds
- Add time pressure elements
- Create level editor for custom maps

## Assignment Questions

1. Explain how the adjacency list representation works in this implementation
2. Trace through Dijkstra's algorithm execution for a specific start-end pair
3. What is the time complexity of the implemented Dijkstra's algorithm?
4. How do random events affect the graph structure and pathfinding?
5. Modify the scoring system to include time-based bonuses

## Troubleshooting

- **Pygame not found**: Install pygame using `pip install pygame`
- **Game runs slowly**: Reduce FPS or simplify graphics
- **Path not connecting**: Ensure you click on adjacent nodes only
- **Events not working**: Check that random events are enabled after round 1

## License

This project is created for educational purposes. Feel free to modify and extend for learning!

**Created for BTech 5th Semester AIML Students**
*Understanding Algorithms Through Interactive Gaming*

---


