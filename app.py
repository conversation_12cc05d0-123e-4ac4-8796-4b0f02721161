"""
Rescue Mission - Path Finder Web Application
Flask backend for the web-based version of the pathfinding game
"""

from flask import Flask, render_template, jsonify, request
import json
import random
import heapq
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from enum import Enum

app = Flask(__name__)

# Node types
class NodeType(Enum):
    HOSPITAL = "Hospital"
    ACCIDENT_SITE = "Accident Site"
    CHECKPOINT = "Checkpoint"

@dataclass
class Node:
    """Represents a location in the city map"""
    id: int
    name: str
    node_type: str
    x: int
    y: int
    color: str

@dataclass
class Edge:
    """Represents a road between two locations"""
    from_node: int
    to_node: int
    weight: float
    original_weight: float
    is_blocked: bool = False

class RescueMissionWebGame:
    """Web version of the Rescue Mission game"""
    
    def __init__(self):
        self.nodes: Dict[int, Node] = {}
        self.edges: List[Edge] = []
        self.adjacency_list: Dict[int, List[Tuple[int, float]]] = {}
        self.create_city_map()
        
    def create_city_map(self):
        """Create the nodes and edges for the city map"""
        # Define node positions and types (scaled for web canvas)
        node_data = [
            # Hospitals (Green)
            (0, "Central Hospital", "Hospital", 150, 200, "#28a745"),
            (1, "City Hospital", "Hospital", 250, 450, "#28a745"),
            (2, "Emergency Center", "Hospital", 200, 650, "#28a745"),
            
            # Accident Sites (Red)
            (3, "Highway Crash", "Accident Site", 950, 150, "#dc3545"),
            (4, "Downtown Accident", "Accident Site", 1050, 350, "#dc3545"),
            (5, "Bridge Incident", "Accident Site", 900, 550, "#dc3545"),
            (6, "Tunnel Crash", "Accident Site", 1000, 700, "#dc3545"),
            
            # Checkpoints (Blue)
            (7, "Police Station", "Checkpoint", 450, 250, "#007bff"),
            (8, "Fire Station", "Checkpoint", 550, 400, "#007bff"),
            (9, "Traffic Control", "Checkpoint", 650, 550, "#007bff"),
            (10, "Emergency Dispatch", "Checkpoint", 750, 300, "#007bff"),
            (11, "Rescue Base", "Checkpoint", 350, 600, "#007bff"),
            (12, "Command Center", "Checkpoint", 800, 650, "#007bff"),
        ]
        
        # Create nodes
        for node_id, name, node_type, x, y, color in node_data:
            self.nodes[node_id] = Node(node_id, name, node_type, x, y, color)
        
        # Define edges with weights (travel time in minutes)
        edge_data = [
            # From hospitals to checkpoints
            (0, 7, 8), (0, 8, 12), (1, 8, 6), (1, 11, 7),
            (2, 11, 5), (2, 9, 10),
            
            # Between checkpoints
            (7, 8, 5), (7, 10, 9), (8, 9, 7), (8, 10, 8),
            (9, 10, 6), (9, 11, 8), (9, 12, 4), (10, 12, 10),
            (11, 12, 9),
            
            # From checkpoints to accident sites
            (7, 3, 15), (8, 4, 12), (9, 5, 8), (10, 3, 7),
            (10, 4, 5), (12, 5, 9), (12, 6, 11),
            
            # Additional connections for more path options
            (0, 1, 10), (1, 2, 8), (3, 4, 6), (4, 5, 7),
            (5, 6, 9), (7, 9, 11), (8, 12, 13),
        ]
        
        # Create edges (bidirectional)
        for from_node, to_node, weight in edge_data:
            self.edges.append(Edge(from_node, to_node, weight, weight))
            self.edges.append(Edge(to_node, from_node, weight, weight))
        
        # Build adjacency list
        self.build_adjacency_list()
    
    def build_adjacency_list(self):
        """Build adjacency list from edges for efficient pathfinding"""
        self.adjacency_list.clear()
        for node_id in self.nodes:
            self.adjacency_list[node_id] = []
        
        for edge in self.edges:
            if not edge.is_blocked:
                self.adjacency_list[edge.from_node].append((edge.to_node, edge.weight))
    
    def dijkstra(self, start: int, end: int) -> Tuple[List[int], float]:
        """Implement Dijkstra's algorithm to find shortest path"""
        distances = {node_id: float('infinity') for node_id in self.nodes}
        previous = {node_id: None for node_id in self.nodes}
        distances[start] = 0
        
        pq = [(0, start)]
        visited = set()
        
        while pq:
            current_distance, current_node = heapq.heappop(pq)
            
            if current_node in visited:
                continue
                
            visited.add(current_node)
            
            if current_node == end:
                path = []
                node = end
                while node is not None:
                    path.append(node)
                    node = previous[node]
                path.reverse()
                return path, distances[end]
            
            for neighbor, weight in self.adjacency_list.get(current_node, []):
                if neighbor not in visited:
                    new_distance = current_distance + weight
                    
                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        previous[neighbor] = current_node
                        heapq.heappush(pq, (new_distance, neighbor))
        
        return [], float('infinity')
    
    def calculate_path_distance(self, path: List[int]) -> float:
        """Calculate total distance for a given path"""
        if len(path) < 2:
            return 0
        
        total_distance = 0
        for i in range(len(path) - 1):
            from_node = path[i]
            to_node = path[i + 1]
            
            for neighbor, weight in self.adjacency_list.get(from_node, []):
                if neighbor == to_node:
                    total_distance += weight
                    break
            else:
                return float('infinity')
        
        return total_distance
    
    def calculate_score(self, player_path: List[int], optimal_distance: float) -> int:
        """Calculate score based on player's path efficiency"""
        if not player_path:
            return 0
        
        player_distance = self.calculate_path_distance(player_path)
        
        if player_distance == float('infinity'):
            return 0
        
        if player_distance == optimal_distance:
            return 100
        
        efficiency = optimal_distance / player_distance
        score = max(0, int(efficiency * 100))
        
        if player_distance < float('infinity'):
            score = max(score, 20)
        
        return min(score, 100)
    
    def apply_random_event(self):
        """Apply random events like roadblocks or traffic"""
        if random.random() < 0.3:
            event_type = random.choice(["roadblock", "traffic"])
            
            if event_type == "roadblock":
                available_edges = [e for e in self.edges if not e.is_blocked]
                if available_edges:
                    edge = random.choice(available_edges)
                    edge.is_blocked = True
                    return f"Roadblock! Path from {self.nodes[edge.from_node].name} to {self.nodes[edge.to_node].name} is blocked!"
            
            elif event_type == "traffic":
                affected_edges = random.sample(self.edges, min(3, len(self.edges)))
                for edge in affected_edges:
                    if not edge.is_blocked:
                        edge.weight = edge.original_weight * random.uniform(1.5, 2.5)
                return "Traffic jam! Increased travel time on some roads."
            
            self.build_adjacency_list()
        
        return None
    
    def reset_events(self):
        """Reset all random events"""
        for edge in self.edges:
            edge.is_blocked = False
            edge.weight = edge.original_weight
        self.build_adjacency_list()
    
    def get_game_data(self):
        """Get all game data for frontend"""
        return {
            'nodes': [asdict(node) for node in self.nodes.values()],
            'edges': [asdict(edge) for edge in self.edges]
        }

# Global game instance
game = RescueMissionWebGame()

@app.route('/')
def index():
    """Main game page"""
    return render_template('index.html')

@app.route('/api/game-data')
def get_game_data():
    """Get initial game data"""
    return jsonify(game.get_game_data())

@app.route('/api/find-path', methods=['POST'])
def find_path():
    """Find optimal path between two nodes"""
    data = request.json
    start = data.get('start')
    end = data.get('end')
    
    if start is None or end is None:
        return jsonify({'error': 'Start and end nodes required'}), 400
    
    path, distance = game.dijkstra(start, end)
    
    return jsonify({
        'path': path,
        'distance': distance,
        'path_names': [game.nodes[node_id].name for node_id in path] if path else []
    })

@app.route('/api/calculate-score', methods=['POST'])
def calculate_score():
    """Calculate score for player's path"""
    data = request.json
    player_path = data.get('player_path', [])
    optimal_distance = data.get('optimal_distance', 0)
    
    score = game.calculate_score(player_path, optimal_distance)
    player_distance = game.calculate_path_distance(player_path)
    
    return jsonify({
        'score': score,
        'player_distance': player_distance
    })

@app.route('/api/random-event', methods=['POST'])
def apply_random_event():
    """Apply a random event to the game"""
    event_message = game.apply_random_event()
    return jsonify({
        'event_message': event_message,
        'game_data': game.get_game_data()
    })

@app.route('/api/reset-events', methods=['POST'])
def reset_events():
    """Reset all random events"""
    game.reset_events()
    return jsonify({
        'message': 'Events reset successfully',
        'game_data': game.get_game_data()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
