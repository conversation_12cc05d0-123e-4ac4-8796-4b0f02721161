"""
Rescue Mission - Path Finder Game
A Pygame-based educational game implementing <PERSON><PERSON><PERSON>'s algorithm for pathfinding.

Author: <PERSON>ech 5th Semester AIML Student Project
Requirements: pygame, networkx (optional - we'll implement our own graph)
"""

import pygame
import math
import random
import heapq
from typing import Dict, List, Tuple, Optional, Set
from enum import Enum
from dataclasses import dataclass

# Initialize Pygame
pygame.init()

# Constants
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
GRAY = (128, 128, 128)
LIGHT_GRAY = (200, 200, 200)
DARK_GRAY = (64, 64, 64)

# Node types
class NodeType(Enum):
    HOSPITAL = "Hospital"
    ACCIDENT_SITE = "Accident Site"
    CHECKPOINT = "Checkpoint"

@dataclass
class Node:
    """Represents a location in the city map"""
    id: int
    name: str
    node_type: NodeType
    x: int
    y: int
    color: Tuple[int, int, int]

@dataclass
class Edge:
    """Represents a road between two locations"""
    from_node: int
    to_node: int
    weight: float
    original_weight: float  # Store original weight for reset
    is_blocked: bool = False

class GameState(Enum):
    MENU = "menu"
    SELECTING_START = "selecting_start"
    SELECTING_END = "selecting_end"
    PLAYING = "playing"
    RESULTS = "results"

class RescueMissionGame:
    """Main game class for Rescue Mission - Path Finder"""
    
    def __init__(self):
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("Rescue Mission - Path Finder")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # Game state
        self.state = GameState.MENU
        self.running = True
        
        # Graph data
        self.nodes: Dict[int, Node] = {}
        self.edges: List[Edge] = []
        self.adjacency_list: Dict[int, List[Tuple[int, float]]] = {}
        
        # Game variables
        self.start_node: Optional[int] = None
        self.end_node: Optional[int] = None
        self.player_path: List[int] = []
        self.optimal_path: List[int] = []
        self.optimal_distance: float = 0
        self.player_distance: float = 0
        self.score: int = 0
        self.total_score: int = 0
        self.round_number: int = 1
        self.max_rounds: int = 3
        
        # Random events
        self.event_timer: int = 0
        self.event_interval: int = 300  # 5 seconds at 60 FPS
        
        # Initialize the city map
        self.create_city_map()
        
    def create_city_map(self):
        """Create the nodes and edges for the city map"""
        # Define node positions and types
        node_data = [
            # Hospitals (Green)
            (0, "Central Hospital", NodeType.HOSPITAL, 100, 150, GREEN),
            (1, "City Hospital", NodeType.HOSPITAL, 200, 400, GREEN),
            (2, "Emergency Center", NodeType.HOSPITAL, 150, 650, GREEN),
            
            # Accident Sites (Red)
            (3, "Highway Crash", NodeType.ACCIDENT_SITE, 900, 100, RED),
            (4, "Downtown Accident", NodeType.ACCIDENT_SITE, 1000, 300, RED),
            (5, "Bridge Incident", NodeType.ACCIDENT_SITE, 850, 500, RED),
            (6, "Tunnel Crash", NodeType.ACCIDENT_SITE, 950, 700, RED),
            
            # Checkpoints (Blue)
            (7, "Police Station", NodeType.CHECKPOINT, 400, 200, BLUE),
            (8, "Fire Station", NodeType.CHECKPOINT, 500, 350, BLUE),
            (9, "Traffic Control", NodeType.CHECKPOINT, 600, 500, BLUE),
            (10, "Emergency Dispatch", NodeType.CHECKPOINT, 700, 250, BLUE),
            (11, "Rescue Base", NodeType.CHECKPOINT, 300, 550, BLUE),
            (12, "Command Center", NodeType.CHECKPOINT, 750, 600, BLUE),
        ]
        
        # Create nodes
        for node_id, name, node_type, x, y, color in node_data:
            self.nodes[node_id] = Node(node_id, name, node_type, x, y, color)
        
        # Define edges with weights (travel time in minutes)
        edge_data = [
            # From hospitals to checkpoints
            (0, 7, 8), (0, 8, 12), (1, 8, 6), (1, 11, 7),
            (2, 11, 5), (2, 9, 10),
            
            # Between checkpoints
            (7, 8, 5), (7, 10, 9), (8, 9, 7), (8, 10, 8),
            (9, 10, 6), (9, 11, 8), (9, 12, 4), (10, 12, 10),
            (11, 12, 9),
            
            # From checkpoints to accident sites
            (7, 3, 15), (8, 4, 12), (9, 5, 8), (10, 3, 7),
            (10, 4, 5), (12, 5, 9), (12, 6, 11),
            
            # Additional connections for more path options
            (0, 1, 10), (1, 2, 8), (3, 4, 6), (4, 5, 7),
            (5, 6, 9), (7, 9, 11), (8, 12, 13),
        ]
        
        # Create edges (bidirectional)
        for from_node, to_node, weight in edge_data:
            self.edges.append(Edge(from_node, to_node, weight, weight))
            self.edges.append(Edge(to_node, from_node, weight, weight))
        
        # Build adjacency list
        self.build_adjacency_list()
    
    def build_adjacency_list(self):
        """Build adjacency list from edges for efficient pathfinding"""
        self.adjacency_list.clear()
        for node_id in self.nodes:
            self.adjacency_list[node_id] = []
        
        for edge in self.edges:
            if not edge.is_blocked:
                self.adjacency_list[edge.from_node].append((edge.to_node, edge.weight))

    def dijkstra(self, start: int, end: int) -> Tuple[List[int], float]:
        """
        Implement Dijkstra's algorithm to find shortest path
        Returns: (path, total_distance)
        """
        # Initialize distances and previous nodes
        distances = {node_id: float('infinity') for node_id in self.nodes}
        previous = {node_id: None for node_id in self.nodes}
        distances[start] = 0

        # Priority queue: (distance, node_id)
        pq = [(0, start)]
        visited = set()

        while pq:
            current_distance, current_node = heapq.heappop(pq)

            if current_node in visited:
                continue

            visited.add(current_node)

            # If we reached the destination, reconstruct path
            if current_node == end:
                path = []
                node = end
                while node is not None:
                    path.append(node)
                    node = previous[node]
                path.reverse()
                return path, distances[end]

            # Check all neighbors
            for neighbor, weight in self.adjacency_list.get(current_node, []):
                if neighbor not in visited:
                    new_distance = current_distance + weight

                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        previous[neighbor] = current_node
                        heapq.heappush(pq, (new_distance, neighbor))

        # No path found
        return [], float('infinity')

    def calculate_path_distance(self, path: List[int]) -> float:
        """Calculate total distance for a given path"""
        if len(path) < 2:
            return 0

        total_distance = 0
        for i in range(len(path) - 1):
            from_node = path[i]
            to_node = path[i + 1]

            # Find edge weight
            for neighbor, weight in self.adjacency_list.get(from_node, []):
                if neighbor == to_node:
                    total_distance += weight
                    break
            else:
                # No direct edge found
                return float('infinity')

        return total_distance

    def apply_random_event(self):
        """Apply random events like roadblocks or traffic"""
        if random.random() < 0.3:  # 30% chance of event
            event_type = random.choice(["roadblock", "traffic"])

            if event_type == "roadblock":
                # Block a random edge
                available_edges = [e for e in self.edges if not e.is_blocked]
                if available_edges:
                    edge = random.choice(available_edges)
                    edge.is_blocked = True
                    print(f"Roadblock! Path from {self.nodes[edge.from_node].name} to {self.nodes[edge.to_node].name} is blocked!")

            elif event_type == "traffic":
                # Increase weight of random edges
                affected_edges = random.sample(self.edges, min(3, len(self.edges)))
                for edge in affected_edges:
                    if not edge.is_blocked:
                        edge.weight = edge.original_weight * random.uniform(1.5, 2.5)
                        print(f"Traffic jam! Increased travel time on some roads.")

            # Rebuild adjacency list after changes
            self.build_adjacency_list()

    def reset_events(self):
        """Reset all random events"""
        for edge in self.edges:
            edge.is_blocked = False
            edge.weight = edge.original_weight
        self.build_adjacency_list()

    def calculate_score(self) -> int:
        """Calculate score based on player's path efficiency"""
        if not self.optimal_path or not self.player_path:
            return 0

        if self.player_distance == float('infinity'):
            return 0

        # Perfect score for optimal path
        if self.player_path == self.optimal_path:
            return 100

        # Score based on efficiency (closer to optimal = higher score)
        efficiency = self.optimal_distance / self.player_distance
        score = max(0, int(efficiency * 100))

        # Bonus for finding any valid path
        if self.player_distance < float('infinity'):
            score = max(score, 20)

        return min(score, 100)

    def get_node_at_position(self, pos: Tuple[int, int]) -> Optional[int]:
        """Get node ID at given mouse position"""
        mouse_x, mouse_y = pos

        for node_id, node in self.nodes.items():
            distance = math.sqrt((mouse_x - node.x) ** 2 + (mouse_y - node.y) ** 2)
            if distance <= 25:  # Node radius
                return node_id

        return None

    def draw_graph(self):
        """Draw the complete graph with nodes and edges"""
        # Draw edges first (so they appear behind nodes)
        for edge in self.edges:
            if edge.from_node < edge.to_node:  # Draw each edge only once
                from_node = self.nodes[edge.from_node]
                to_node = self.nodes[edge.to_node]

                # Choose edge color based on state
                edge_color = GRAY
                if edge.is_blocked:
                    edge_color = RED
                elif edge.weight > edge.original_weight:
                    edge_color = ORANGE  # Traffic

                # Draw edge
                pygame.draw.line(self.screen, edge_color,
                               (from_node.x, from_node.y),
                               (to_node.x, to_node.y), 3)

                # Draw weight label
                mid_x = (from_node.x + to_node.x) // 2
                mid_y = (from_node.y + to_node.y) // 2
                weight_text = self.small_font.render(f"{edge.weight:.0f}", True, BLACK)
                self.screen.blit(weight_text, (mid_x - 10, mid_y - 10))

        # Draw nodes
        for node_id, node in self.nodes.items():
            # Node circle
            pygame.draw.circle(self.screen, node.color, (node.x, node.y), 25)
            pygame.draw.circle(self.screen, BLACK, (node.x, node.y), 25, 2)

            # Highlight selected nodes
            if node_id == self.start_node:
                pygame.draw.circle(self.screen, YELLOW, (node.x, node.y), 30, 4)
            elif node_id == self.end_node:
                pygame.draw.circle(self.screen, PURPLE, (node.x, node.y), 30, 4)
            elif node_id in self.player_path:
                pygame.draw.circle(self.screen, WHITE, (node.x, node.y), 28, 3)

            # Node ID
            id_text = self.small_font.render(str(node_id), True, WHITE)
            text_rect = id_text.get_rect(center=(node.x, node.y))
            self.screen.blit(id_text, text_rect)

            # Node name (below the node)
            name_text = self.small_font.render(node.name, True, BLACK)
            name_rect = name_text.get_rect(center=(node.x, node.y + 40))
            self.screen.blit(name_text, name_rect)

    def draw_path(self, path: List[int], color: Tuple[int, int, int], width: int = 5):
        """Draw a path through the graph"""
        if len(path) < 2:
            return

        for i in range(len(path) - 1):
            from_node = self.nodes[path[i]]
            to_node = self.nodes[path[i + 1]]
            pygame.draw.line(self.screen, color,
                           (from_node.x, from_node.y),
                           (to_node.x, to_node.y), width)

    def draw_ui(self):
        """Draw the user interface"""
        # Title
        title_text = self.font.render("Rescue Mission - Path Finder", True, BLACK)
        self.screen.blit(title_text, (10, 10))

        # Instructions based on game state
        if self.state == GameState.MENU:
            instructions = [
                "Welcome to Rescue Mission!",
                "Click 'Start Game' to begin",
                "Find the shortest path from hospital to accident site",
                "Green = Hospitals, Red = Accident Sites, Blue = Checkpoints"
            ]
        elif self.state == GameState.SELECTING_START:
            instructions = [
                f"Round {self.round_number}/{self.max_rounds}",
                "Select a HOSPITAL (green) as starting point",
                "Click on any green node to start your rescue mission"
            ]
        elif self.state == GameState.SELECTING_END:
            instructions = [
                f"Start: {self.nodes[self.start_node].name}",
                "Select an ACCIDENT SITE (red) as destination",
                "Click on any red node to set your target"
            ]
        elif self.state == GameState.PLAYING:
            instructions = [
                f"Start: {self.nodes[self.start_node].name}",
                f"Destination: {self.nodes[self.end_node].name}",
                "Click nodes to build your path",
                f"Current path: {' -> '.join([str(n) for n in self.player_path])}",
                "Press SPACE when done, R to reset path"
            ]
        elif self.state == GameState.RESULTS:
            instructions = [
                f"Round {self.round_number} Results:",
                f"Your path distance: {self.player_distance:.1f}",
                f"Optimal path distance: {self.optimal_distance:.1f}",
                f"Round score: {self.score}",
                f"Total score: {self.total_score}",
                "Press ENTER for next round or ESC to quit"
            ]

        # Draw instructions
        y_offset = 50
        for instruction in instructions:
            text = self.small_font.render(instruction, True, BLACK)
            self.screen.blit(text, (10, y_offset))
            y_offset += 25

    def handle_click(self, pos: Tuple[int, int]):
        """Handle mouse clicks based on current game state"""
        clicked_node = self.get_node_at_position(pos)

        if clicked_node is None:
            return

        if self.state == GameState.SELECTING_START:
            # Only allow hospitals as start nodes
            if self.nodes[clicked_node].node_type == NodeType.HOSPITAL:
                self.start_node = clicked_node
                self.player_path = [clicked_node]
                self.state = GameState.SELECTING_END

        elif self.state == GameState.SELECTING_END:
            # Only allow accident sites as end nodes
            if self.nodes[clicked_node].node_type == NodeType.ACCIDENT_SITE:
                self.end_node = clicked_node
                # Calculate optimal path
                self.optimal_path, self.optimal_distance = self.dijkstra(self.start_node, self.end_node)
                self.state = GameState.PLAYING

        elif self.state == GameState.PLAYING:
            # Add node to player's path if it's connected to the last node
            if len(self.player_path) == 0:
                self.player_path = [clicked_node]
            else:
                last_node = self.player_path[-1]

                # Check if there's a direct connection
                connected = False
                for neighbor, _ in self.adjacency_list.get(last_node, []):
                    if neighbor == clicked_node:
                        connected = True
                        break

                if connected and clicked_node not in self.player_path:
                    self.player_path.append(clicked_node)
                elif clicked_node == last_node:
                    # Allow clicking the same node to remove it (undo last move)
                    pass
                elif clicked_node in self.player_path:
                    # If clicking a node already in path, truncate path to that point
                    try:
                        index = self.player_path.index(clicked_node)
                        self.player_path = self.player_path[:index + 1]
                    except ValueError:
                        pass

    def start_new_round(self):
        """Start a new round of the game"""
        self.start_node = None
        self.end_node = None
        self.player_path = []
        self.optimal_path = []
        self.optimal_distance = 0
        self.player_distance = 0
        self.score = 0
        self.state = GameState.SELECTING_START

        # Apply random events
        self.reset_events()
        if self.round_number > 1:  # No events in first round
            self.apply_random_event()

    def finish_round(self):
        """Finish current round and calculate results"""
        self.player_distance = self.calculate_path_distance(self.player_path)
        self.score = self.calculate_score()
        self.total_score += self.score
        self.state = GameState.RESULTS

    def run(self):
        """Main game loop"""
        while self.running:
            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False

                elif event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:  # Left click
                        self.handle_click(event.pos)

                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.running = False

                    elif event.key == pygame.K_SPACE and self.state == GameState.PLAYING:
                        # Finish current round
                        if len(self.player_path) >= 2 and self.player_path[-1] == self.end_node:
                            self.finish_round()

                    elif event.key == pygame.K_r and self.state == GameState.PLAYING:
                        # Reset player path
                        self.player_path = [self.start_node] if self.start_node is not None else []

                    elif event.key == pygame.K_RETURN:
                        if self.state == GameState.MENU:
                            self.start_new_round()
                        elif self.state == GameState.RESULTS:
                            if self.round_number < self.max_rounds:
                                self.round_number += 1
                                self.start_new_round()
                            else:
                                # Game over
                                print(f"Game Over! Final Score: {self.total_score}/{self.max_rounds * 100}")
                                self.running = False

            # Update
            self.event_timer += 1

            # Draw everything
            self.screen.fill(WHITE)

            if self.state != GameState.MENU:
                self.draw_graph()

                # Draw optimal path in green (only in results)
                if self.state == GameState.RESULTS and self.optimal_path:
                    self.draw_path(self.optimal_path, GREEN, 8)

                # Draw player path in blue
                if self.player_path and len(self.player_path) > 1:
                    self.draw_path(self.player_path, BLUE, 6)

            self.draw_ui()

            # Draw start button in menu
            if self.state == GameState.MENU:
                button_rect = pygame.Rect(WINDOW_WIDTH // 2 - 100, WINDOW_HEIGHT // 2, 200, 50)
                pygame.draw.rect(self.screen, GREEN, button_rect)
                pygame.draw.rect(self.screen, BLACK, button_rect, 2)
                button_text = self.font.render("Start Game", True, BLACK)
                text_rect = button_text.get_rect(center=button_rect.center)
                self.screen.blit(button_text, text_rect)

                # Handle button click
                mouse_pos = pygame.mouse.get_pos()
                if pygame.mouse.get_pressed()[0] and button_rect.collidepoint(mouse_pos):
                    self.start_new_round()

            pygame.display.flip()
            self.clock.tick(FPS)

        pygame.quit()

# Main execution
if __name__ == "__main__":
    game = RescueMissionGame()
    game.run()
